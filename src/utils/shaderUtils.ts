import * as THREE from 'three';

/**
 * Shader工具函数和程序化纹理实现
 */

// 通用的噪声函数
export const noiseShaderChunk = `
// 简化的噪声函数
float random(vec2 st) {
    return fract(sin(dot(st.xy, vec2(12.9898,78.233))) * 43758.5453123);
}

float noise(vec2 st) {
    vec2 i = floor(st);
    vec2 f = fract(st);
    
    float a = random(i);
    float b = random(i + vec2(1.0, 0.0));
    float c = random(i + vec2(0.0, 1.0));
    float d = random(i + vec2(1.0, 1.0));
    
    vec2 u = f * f * (3.0 - 2.0 * f);
    
    return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
}

float fbm(vec2 st) {
    float value = 0.0;
    float amplitude = 0.5;
    float frequency = 0.0;
    
    for (int i = 0; i < 4; i++) {
        value += amplitude * noise(st);
        st *= 2.0;
        amplitude *= 0.5;
    }
    return value;
}
`;

// Voronoi噪声
export const voronoiShaderChunk = `
vec2 voronoi(vec2 st) {
    vec2 i_st = floor(st);
    vec2 f_st = fract(st);
    
    float m_dist = 1.0;
    vec2 m_point;
    
    for (int j = -1; j <= 1; j++) {
        for (int i = -1; i <= 1; i++) {
            vec2 neighbor = vec2(float(i), float(j));
            vec2 point = random2(i_st + neighbor);
            point = 0.5 + 0.5 * sin(u_time + 6.2831 * point);
            vec2 diff = neighbor + point - f_st;
            float dist = length(diff);
            
            if (dist < m_dist) {
                m_dist = dist;
                m_point = point;
            }
        }
    }
    
    return vec2(m_dist, 0.0);
}

vec2 random2(vec2 st) {
    st = vec2(dot(st, vec2(127.1, 311.7)), dot(st, vec2(269.5, 183.3)));
    return -1.0 + 2.0 * fract(sin(st) * 43758.5453123);
}
`;

/**
 * 程序化纹理材质创建器
 */
export class ProceduralMaterialFactory {
  /**
   * 创建噪声材质
   */
  static createNoiseMaterial(options: {
    scale?: number;
    octaves?: number;
    persistence?: number;
    lacunarity?: number;
    color1?: THREE.Color;
    color2?: THREE.Color;
    animated?: boolean;
  } = {}): THREE.ShaderMaterial {
    const {
      scale = 1.0,
      octaves = 4,
      persistence = 0.5,
      lacunarity = 2.0,
      color1 = new THREE.Color(0x000000),
      color2 = new THREE.Color(0xffffff),
      animated = false
    } = options;

    const vertexShader = `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;

    const fragmentShader = `
      uniform float u_time;
      uniform float u_scale;
      uniform int u_octaves;
      uniform float u_persistence;
      uniform float u_lacunarity;
      uniform vec3 u_color1;
      uniform vec3 u_color2;
      varying vec2 vUv;
      
      ${noiseShaderChunk}
      
      void main() {
        vec2 st = vUv * u_scale;
        ${animated ? 'st += u_time * 0.1;' : ''}
        
        float n = 0.0;
        float amplitude = 1.0;
        float frequency = 1.0;
        float maxValue = 0.0;
        
        for (int i = 0; i < 8; i++) {
          if (i >= u_octaves) break;
          n += amplitude * noise(st * frequency);
          maxValue += amplitude;
          amplitude *= u_persistence;
          frequency *= u_lacunarity;
        }
        
        n /= maxValue;
        vec3 color = mix(u_color1, u_color2, n);
        gl_FragColor = vec4(color, 1.0);
      }
    `;

    return new THREE.ShaderMaterial({
      uniforms: {
        u_time: { value: 0.0 },
        u_scale: { value: scale },
        u_octaves: { value: octaves },
        u_persistence: { value: persistence },
        u_lacunarity: { value: lacunarity },
        u_color1: { value: color1 },
        u_color2: { value: color2 }
      },
      vertexShader,
      fragmentShader
    });
  }

  /**
   * 创建Voronoi材质
   */
  static createVoronoiMaterial(options: {
    scale?: number;
    color1?: THREE.Color;
    color2?: THREE.Color;
    animated?: boolean;
  } = {}): THREE.ShaderMaterial {
    const {
      scale = 5.0,
      color1 = new THREE.Color(0x000000),
      color2 = new THREE.Color(0xffffff)
    } = options;

    const vertexShader = `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;

    const fragmentShader = `
      uniform float u_time;
      uniform float u_scale;
      uniform vec3 u_color1;
      uniform vec3 u_color2;
      varying vec2 vUv;
      
      ${voronoiShaderChunk}
      
      void main() {
        vec2 st = vUv * u_scale;
        vec2 v = voronoi(st);
        float n = v.x;
        
        vec3 color = mix(u_color1, u_color2, n);
        gl_FragColor = vec4(color, 1.0);
      }
    `;

    return new THREE.ShaderMaterial({
      uniforms: {
        u_time: { value: 0.0 },
        u_scale: { value: scale },
        u_color1: { value: color1 },
        u_color2: { value: color2 }
      },
      vertexShader,
      fragmentShader
    });
  }

  /**
   * 创建木纹材质
   */
  static createWoodMaterial(options: {
    scale?: number;
    rings?: number;
    color1?: THREE.Color;
    color2?: THREE.Color;
  } = {}): THREE.ShaderMaterial {
    const {
      scale = 1.0,
      rings = 10.0,
      color1 = new THREE.Color(0x8B4513),
      color2 = new THREE.Color(0xDEB887)
    } = options;

    const vertexShader = `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;

    const fragmentShader = `
      uniform float u_scale;
      uniform float u_rings;
      uniform vec3 u_color1;
      uniform vec3 u_color2;
      varying vec2 vUv;
      
      ${noiseShaderChunk}
      
      void main() {
        vec2 st = vUv * u_scale;
        
        // 创建径向距离
        vec2 center = vec2(0.5);
        float dist = distance(st, center);
        
        // 添加噪声扰动
        float n = noise(st * 8.0) * 0.1;
        dist += n;
        
        // 创建环形图案
        float rings = sin(dist * u_rings * 3.14159) * 0.5 + 0.5;
        
        // 添加更多噪声细节
        float detail = noise(st * 32.0) * 0.3;
        rings += detail;
        
        vec3 color = mix(u_color1, u_color2, rings);
        gl_FragColor = vec4(color, 1.0);
      }
    `;

    return new THREE.ShaderMaterial({
      uniforms: {
        u_scale: { value: scale },
        u_rings: { value: rings },
        u_color1: { value: color1 },
        u_color2: { value: color2 }
      },
      vertexShader,
      fragmentShader
    });
  }

  /**
   * 创建大理石材质
   */
  static createMarbleMaterial(options: {
    scale?: number;
    turbulence?: number;
    color1?: THREE.Color;
    color2?: THREE.Color;
  } = {}): THREE.ShaderMaterial {
    const {
      scale = 1.0,
      turbulence = 0.5,
      color1 = new THREE.Color(0xffffff),
      color2 = new THREE.Color(0x888888)
    } = options;

    const vertexShader = `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;

    const fragmentShader = `
      uniform float u_scale;
      uniform float u_turbulence;
      uniform vec3 u_color1;
      uniform vec3 u_color2;
      varying vec2 vUv;
      
      ${noiseShaderChunk}
      
      void main() {
        vec2 st = vUv * u_scale;
        
        // 基础大理石图案
        float marble = sin((st.x + fbm(st) * u_turbulence) * 3.14159);
        marble = marble * 0.5 + 0.5;
        
        // 添加细节
        float detail = fbm(st * 4.0) * 0.2;
        marble += detail;
        
        vec3 color = mix(u_color1, u_color2, marble);
        gl_FragColor = vec4(color, 1.0);
      }
    `;

    return new THREE.ShaderMaterial({
      uniforms: {
        u_scale: { value: scale },
        u_turbulence: { value: turbulence },
        u_color1: { value: color1 },
        u_color2: { value: color2 }
      },
      vertexShader,
      fragmentShader
    });
  }
}

/**
 * Shader动画管理器
 */
export class ShaderAnimationManager {
  private materials: THREE.ShaderMaterial[] = [];
  private animationId: number | null = null;
  private startTime: number = Date.now();

  addMaterial(material: THREE.ShaderMaterial) {
    this.materials.push(material);
    
    if (this.materials.length === 1) {
      this.startAnimation();
    }
  }

  removeMaterial(material: THREE.ShaderMaterial) {
    const index = this.materials.indexOf(material);
    if (index > -1) {
      this.materials.splice(index, 1);
    }
    
    if (this.materials.length === 0) {
      this.stopAnimation();
    }
  }

  private startAnimation() {
    const animate = () => {
      const time = (Date.now() - this.startTime) * 0.001;
      
      this.materials.forEach(material => {
        if (material.uniforms.u_time) {
          material.uniforms.u_time.value = time;
        }
      });
      
      this.animationId = requestAnimationFrame(animate);
    };
    
    animate();
  }

  private stopAnimation() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  dispose() {
    this.stopAnimation();
    this.materials = [];
  }
}

// 全局shader动画管理器
export const globalShaderAnimationManager = new ShaderAnimationManager();
