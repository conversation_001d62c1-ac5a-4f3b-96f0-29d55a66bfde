import { useState, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * 通用的Logo点击处理Hook
 * 实现三次点击跳转到后台管理的功能
 */
export const useLogoClick = (targetPath: string = '/admin') => {
  const [logoClickCount, setLogoClickCount] = useState(0);
  const logoClickTimer = useRef<number | null>(null);
  const navigate = useNavigate();

  const handleLogoClick = useCallback(() => {
    const newCount = logoClickCount + 1;
    setLogoClickCount(newCount);
    
    // 清除之前的计时器
    if (logoClickTimer.current) {
      window.clearTimeout(logoClickTimer.current);
    }
    
    // 设置新的计时器，2秒内未达到3次点击则重置计数
    logoClickTimer.current = window.setTimeout(() => {
      setLogoClickCount(0);
    }, 2000);
    
    // 如果达到3次点击，跳转到目标页面
    if (newCount === 3) {
      setLogoClickCount(0);
      if (logoClickTimer.current) {
        window.clearTimeout(logoClickTimer.current);
      }
      navigate(targetPath);
    }
  }, [logoClickCount, navigate, targetPath]);

  // 清理定时器
  const cleanup = useCallback(() => {
    if (logoClickTimer.current) {
      window.clearTimeout(logoClickTimer.current);
      logoClickTimer.current = null;
    }
  }, []);

  return {
    handleLogoClick,
    logoClickCount,
    cleanup
  };
};
