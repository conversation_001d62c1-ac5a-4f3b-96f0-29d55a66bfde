import { useState, useCallback, useRef, useEffect } from 'react';
import { safeExecute, AppError, classifyError } from '../utils/errorHandling';

export interface AsyncOperationState<T> {
  data: T | null;
  loading: boolean;
  error: AppError | null;
  lastUpdated: number | null;
}

export interface AsyncOperationOptions {
  /** 操作ID，用于错误恢复 */
  operationId?: string;
  /** 是否在组件挂载时立即执行 */
  immediate?: boolean;
  /** 缓存时间（毫秒） */
  cacheTime?: number;
  /** 是否启用重试 */
  enableRetry?: boolean;
  /** 自定义错误处理 */
  onError?: (error: AppError) => void;
  /** 成功回调 */
  onSuccess?: <T>(data: T) => void;
}

/**
 * 异步操作管理Hook
 * 提供加载状态、错误处理、重试机制和缓存功能
 */
export const useAsyncOperation = <T>(
  operation: () => Promise<T>,
  options: AsyncOperationOptions = {}
) => {
  const {
    operationId,
    immediate = false,
    cacheTime = 5 * 60 * 1000, // 5分钟默认缓存
    enableRetry = true,
    onError,
    onSuccess
  } = options;

  const [state, setState] = useState<AsyncOperationState<T>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const operationRef = useRef(operation);
  const retryCountRef = useRef(0);

  // 更新操作引用
  useEffect(() => {
    operationRef.current = operation;
  }, [operation]);

  // 清理函数
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // 检查缓存是否有效
  const isCacheValid = useCallback(() => {
    if (!state.lastUpdated || !cacheTime) return false;
    return Date.now() - state.lastUpdated < cacheTime;
  }, [state.lastUpdated, cacheTime]);

  // 执行操作
  const execute = useCallback(async (forceRefresh = false): Promise<T | null> => {
    // 如果有有效缓存且不强制刷新，返回缓存数据
    if (!forceRefresh && state.data && isCacheValid()) {
      return state.data;
    }

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // 创建新的AbortController
    abortControllerRef.current = new AbortController();

    setState(prev => ({
      ...prev,
      loading: true,
      error: null
    }));

    try {
      const result = await safeExecute(
        operationRef.current,
        operationId,
        (error) => {
          setState(prev => ({
            ...prev,
            loading: false,
            error
          }));
          onError?.(error);
        }
      );

      if (result !== null) {
        setState({
          data: result,
          loading: false,
          error: null,
          lastUpdated: Date.now()
        });
        
        onSuccess?.(result);
        retryCountRef.current = 0; // 重置重试计数
        return result;
      }

      return null;
    } catch (error) {
      const appError = classifyError(error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: appError
      }));
      
      onError?.(appError);
      return null;
    }
  }, [state.data, isCacheValid, operationId, onError, onSuccess]);

  // 重试操作
  const retry = useCallback(async (): Promise<T | null> => {
    if (!enableRetry) {
      console.warn('Retry is disabled for this operation');
      return null;
    }

    retryCountRef.current++;
    console.log(`Retrying operation (attempt ${retryCountRef.current})`);
    
    return execute(true);
  }, [execute, enableRetry]);

  // 重置状态
  const reset = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null
    });
    
    retryCountRef.current = 0;
  }, []);

  // 清除缓存
  const clearCache = useCallback(() => {
    setState(prev => ({
      ...prev,
      data: null,
      lastUpdated: null
    }));
  }, []);

  // 立即执行（如果启用）
  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [immediate]); // 只在immediate变化时执行

  // 计算派生状态
  const isStale = !isCacheValid();
  const canRetry = enableRetry && state.error && !state.loading;
  const hasData = state.data !== null;

  return {
    // 状态
    ...state,
    isStale,
    canRetry,
    hasData,
    retryCount: retryCountRef.current,
    
    // 操作
    execute,
    retry,
    reset,
    clearCache,
    
    // 工具方法
    refresh: () => execute(true),
    refetch: () => execute(true)
  };
};

/**
 * 批量异步操作Hook
 */
export const useBatchAsyncOperation = <T>(
  operations: Array<() => Promise<T>>,
  options: AsyncOperationOptions = {}
) => {
  const [states, setStates] = useState<AsyncOperationState<T>[]>(
    operations.map(() => ({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null
    }))
  );

  const execute = useCallback(async (): Promise<(T | null)[]> => {
    setStates(prev => prev.map(state => ({ ...state, loading: true, error: null })));

    const results = await Promise.allSettled(
      operations.map(async (operation, index) => {
        try {
          const result = await safeExecute(
            operation,
            `${options.operationId}_${index}`,
            (error) => {
              setStates(prev => prev.map((state, i) => 
                i === index ? { ...state, loading: false, error } : state
              ));
              options.onError?.(error);
            }
          );
          
          if (result !== null) {
            setStates(prev => prev.map((state, i) => 
              i === index ? {
                data: result,
                loading: false,
                error: null,
                lastUpdated: Date.now()
              } : state
            ));
            return result;
          }
          
          return null;
        } catch (error) {
          const appError = classifyError(error);
          setStates(prev => prev.map((state, i) => 
            i === index ? { ...state, loading: false, error: appError } : state
          ));
          return null;
        }
      })
    );

    return results.map(result => 
      result.status === 'fulfilled' ? result.value : null
    );
  }, [operations, options.operationId, options.onError]);

  const reset = useCallback(() => {
    setStates(operations.map(() => ({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null
    })));
  }, [operations]);

  const allLoading = states.every(state => state.loading);
  const anyLoading = states.some(state => state.loading);
  const allSuccess = states.every(state => state.data !== null && !state.error);
  const anyError = states.some(state => state.error !== null);

  return {
    states,
    allLoading,
    anyLoading,
    allSuccess,
    anyError,
    execute,
    reset
  };
};
