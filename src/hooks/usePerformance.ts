import { useEffect, useRef, useCallback } from 'react';

export interface PerformanceMetrics {
  renderTime: number;
  componentName: string;
  timestamp: number;
}

/**
 * 性能监控Hook
 * 用于监控组件渲染性能和用户交互性能
 */
export const usePerformance = (componentName: string) => {
  const renderStartTime = useRef<number>(performance.now());
  const metricsRef = useRef<PerformanceMetrics[]>([]);

  // 记录渲染性能
  useEffect(() => {
    const renderTime = performance.now() - renderStartTime.current;
    const metrics: PerformanceMetrics = {
      renderTime,
      componentName,
      timestamp: Date.now()
    };
    
    metricsRef.current.push(metrics);
    
    // 只保留最近100条记录
    if (metricsRef.current.length > 100) {
      metricsRef.current = metricsRef.current.slice(-100);
    }
    
    // 在开发环境下输出性能警告
    if (process.env.NODE_ENV === 'development' && renderTime > 16) {
      console.warn(`Performance warning: ${componentName} took ${renderTime.toFixed(2)}ms to render`);
    }
  });

  // 测量异步操作性能
  const measureAsync = useCallback(async <T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> => {
    const startTime = performance.now();
    try {
      const result = await operation();
      const duration = performance.now() - startTime;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName}.${operationName} took ${duration.toFixed(2)}ms`);
      }
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      console.error(`${componentName}.${operationName} failed after ${duration.toFixed(2)}ms:`, error);
      throw error;
    }
  }, [componentName]);

  // 测量同步操作性能
  const measureSync = useCallback(<T>(
    operation: () => T,
    operationName: string
  ): T => {
    const startTime = performance.now();
    try {
      const result = operation();
      const duration = performance.now() - startTime;
      
      if (process.env.NODE_ENV === 'development' && duration > 5) {
        console.warn(`${componentName}.${operationName} took ${duration.toFixed(2)}ms (sync operation)`);
      }
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      console.error(`${componentName}.${operationName} failed after ${duration.toFixed(2)}ms:`, error);
      throw error;
    }
  }, [componentName]);

  // 获取性能统计
  const getMetrics = useCallback(() => {
    const metrics = metricsRef.current;
    if (metrics.length === 0) return null;
    
    const renderTimes = metrics.map(m => m.renderTime);
    const avgRenderTime = renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length;
    const maxRenderTime = Math.max(...renderTimes);
    const minRenderTime = Math.min(...renderTimes);
    
    return {
      componentName,
      totalRenders: metrics.length,
      avgRenderTime: Number(avgRenderTime.toFixed(2)),
      maxRenderTime: Number(maxRenderTime.toFixed(2)),
      minRenderTime: Number(minRenderTime.toFixed(2)),
      lastRenderTime: Number(renderTimes[renderTimes.length - 1]?.toFixed(2) || 0)
    };
  }, [componentName]);

  return {
    measureAsync,
    measureSync,
    getMetrics
  };
};
