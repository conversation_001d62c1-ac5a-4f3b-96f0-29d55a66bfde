/* 页面布局 */
.render-page {
  /* position: fixed; inset: 0; 已经能确保元素占满视口，但保留width/height以增强兼容性 */
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: var(--spacing-lg);
  box-sizing: border-box;
  background: var(--color-bg-page);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  color: var(--color-content-accent);
  position: fixed;
  inset: 0;
}

/* 标题栏 */
.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: var(--spacing-sm);
  width: 100%;
  margin-bottom: var(--spacing-lg);
}

.title-bar__left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo {
  width: 78px;
  height: 14px;
  transition: all 0.2s ease;
  object-fit: contain;
}

.logo--clickable {
  cursor: pointer;
  height: var(--button-height);
}

.user-controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 主要内容区域 */
.render-container {
  display: flex;
  flex: 1;
  gap: var(--spacing-lg);
  overflow: hidden;
  /* 显式设置padding和margin为0，确保在不同浏览器中行为一致 */
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  width: 100%;
}

/* 渲染窗口 */
.render-window {
  flex: 1;
  min-width: 0;
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.button-container {
  position: absolute;
  right: var(--spacing-lg);
  bottom: var(--spacing-lg);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--spacing-sm);
}

.control-button {
  padding: var(--spacing-xs) var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-s);
  background: var(--color-bg-overlay);
  border-radius: var(--radius-full);
  cursor: pointer;
}

.control-button span {
  color: var(--color-content-regular);
  font-size: var(--font-size-base);
}

.icon-wrapper {
  width: var(--spacing-md);
  height: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-content-regular);
}

/* 属性面板 */
.property-panel {
  width: 220px;
  flex-shrink: 0;
  padding: var(--spacing-base);
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  overflow-y: auto;
  overflow-x: hidden;
}

.panel-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  width: 100%;
}

.section-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.section-header span {
  color: var(--color-content-regular);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

.dropdown-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.upload-button-wrapper,
.search-wrapper {
  width: 100%;
}

.uploaded-model-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--color-bg-primary);
  border-radius: var(--radius-base);
  padding: var(--spacing-sm) var(--spacing-md);
  width: 100%;
}

.uploaded-model-name {
  color: var(--color-content-regular);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  margin-right: var(--spacing-sm);
}



.bottom-buttons {
  margin-top: auto;
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  padding-top: var(--spacing-base);
}

/* 材质网格 */
.materials-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-base);
  /* min-height: 0确保flex子项可以正确收缩，即使内容溢出也不会导致容器增长 */
  min-height: 0;
  /* 允许内容在必要时溢出容器 */
  overflow: visible;
}




/* 渲染区域 */
.render-area {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-bg-primary);
  /* 移除不完整的背景设置，因为没有设置background-image */
  overflow: hidden;
  position: relative;
}

.render-area canvas {
  /* !important用于覆盖可能来自第三方库的canvas样式 */
  width: 100% !important;
  height: 100% !important;
  outline: none;
}

/* 加载状态样式已移至通用Loading组件 */

/* 自定义材质状态 */
.custom-material-disabled {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxxl) var(--spacing-lg);
  text-align: center;
}

.disabled-message {
  color: var(--color-content-mute);
  font-size: var(--font-size-base);
  font-style: italic;
}

/* 无材质状态 */
.no-materials {
  text-align: center;
  color: var(--color-content-mute);
  font-size: var(--font-size-base);
  padding: var(--spacing-lg);
}