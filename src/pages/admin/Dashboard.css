.dashboard-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  background-color: var(--color-bg-page);
  overflow-x: hidden;
}



.dashboard-sidebar {
  display: flex;
  flex-direction: column;
  width: 220px;
  background-color: var(--color-bg-primary);
  border-right: 1px solid var(--color-border);
  padding: var(--spacing-lg) 0;
}

.sidebar-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-xxxl);
}

.logo-container {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s;
}

.logo-tooltip {
  position: absolute;
  bottom: -25px;
  background-color: var(--color-support);
  color: var(--color-content-accent);
  padding: 4px 8px;
  border-radius: var(--radius-base);
  font-size: 14px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-5px);
  transition: all 0.2s ease;
  white-space: nowrap;
  z-index: 100;
}

.sidebar-header .logo {
  height: 40px;
  margin-bottom: 12px;
}

.sidebar-header h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-accent);
  margin: 0;
}

.sidebar-menu {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  list-style: none;
}

/* 自定义菜单项 */
.menu-item {
  width: 100%;
  height: var(--button-height-large);
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
  padding: 0 var(--spacing-xl);
  cursor: pointer;
  transition: background-color 0.2s;
  color: var(--color-content-regular);
}

.menu-item:hover {
  background-color: var(--color-bg-hover);
}

.menu-item.active {
  background-color: var(--color-support);
  color: var(--color-content-accent);
}


.sidebar-footer {
  padding: 0 var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.logout-button {
  display: flex;
  align-items: center;
  height: var(--button-height-large);
  gap: var(--spacing-base);
  padding: var(--spacing-base) var(--spacing-md);
  border-radius: var(--radius-base);
  cursor: pointer;
  color: var(--color-content-regular);
  transition: all 0.2s ease;
}

.logout-button:hover {
  background-color: var(--color-support-hover);
  color: var(--color-content-accent);
}

.dashboard-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xxxl);
  border-bottom: 1px solid var(--color-border);
}

.content-header h1 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-accent);
  margin: 0;
}

.content-body {
  flex: 1;
  padding: var(--spacing-xl) var(--spacing-xxxl);
  overflow-y: auto;
}

/* 总览页面样式 */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: var(--color-bg-dialog);
  border-radius: var(--radius-base);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: var(--color-brand);
  border-radius: var(--radius-base);
  color: var(--color-content-invert);
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-accent);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-content-regular);
}

.quick-actions {
  background-color: var(--color-bg-dialog);
  border-radius: var(--radius-base);
  padding: 20px;
}

.quick-actions h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-accent);
  margin-top: 0;
  margin-bottom: 16px;
}

.quick-action-links {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.quick-action-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px;
  background-color: var(--color-bg-primary);
  border-radius: var(--radius-base);
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--color-content-regular);
}

.quick-action-item:hover {
  background-color: var(--color-support);
  color: var(--color-content-accent);
}