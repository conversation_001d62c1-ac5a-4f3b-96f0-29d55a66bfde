.slider {
  display: flex;
  flex-direction: column;
  gap: 12px;
  cursor: pointer;
  position: relative;
  height: 30px;
}

.slider__bg,
.slider__drag {
  height: 100%;
  position: absolute;
  border-radius: var(--radius-base);
  left: 0;
  top: 0;
}

.slider__bg {
  width: 100%;
  background: var(--color-bg-overlay);
}

.slider__drag {
  background: var(--color-content-secondary);
  /* width is dynamically set in TSX */
}

/* 数值样式 */
.slider__value {
  font-size: var(--font-size-base);
  color: var(--color-content-secondary);
  text-align: left;
  margin-top: 8px;
  user-select: none;
}

/* 禁用状态 */
.slider--disabled {
  opacity: 0.5;
  pointer-events: none;
}

