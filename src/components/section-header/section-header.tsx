import React from 'react';
import type { LucideIcon } from 'lucide-react';
import './section-header.css';

export interface SectionHeaderProps {
  /** 图标组件 */
  icon?: LucideIcon;
  /** 标题文本 */
  title: string;
  /** 副标题或描述 */
  subtitle?: string;
  /** 右侧操作区域 */
  actions?: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 尺寸变体 */
  size?: 'small' | 'medium' | 'large';
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  icon: Icon,
  title,
  subtitle,
  actions,
  className = '',
  size = 'medium'
}) => {
  const containerClasses = [
    'section-header',
    `section-header--${size}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      <div className="section-header__content">
        <div className="section-header__main">
          {Icon && (
            <div className="section-header__icon">
              <Icon className="section-header__icon-svg" />
            </div>
          )}
          <div className="section-header__text">
            <h3 className="section-header__title">{title}</h3>
            {subtitle && (
              <p className="section-header__subtitle">{subtitle}</p>
            )}
          </div>
        </div>
        {actions && (
          <div className="section-header__actions">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};
