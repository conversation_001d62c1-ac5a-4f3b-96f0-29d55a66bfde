import React from 'react';
import './materials-grid.css';

export interface MaterialsGridProps {
  /** 网格内容 */
  children: React.ReactNode;
  /** 网格变体 */
  variant?: 'default' | 'preset';
  /** 自定义类名 */
  className?: string;
}

/**
 * 材质网格组件
 * 提供统一的材质展示网格布局
 */
export const MaterialsGrid: React.FC<MaterialsGridProps> = ({
  children,
  variant = 'default',
  className = '',
}) => {
  const containerClasses = [
    'materials-grid',
    `materials-grid--${variant}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {children}
    </div>
  );
};

export default MaterialsGrid;
