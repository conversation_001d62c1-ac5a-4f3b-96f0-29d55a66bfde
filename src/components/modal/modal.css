/* Shared styles for admin management modals */

/* --- Animations --- */
@keyframes modal-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modal-slide-in {
  from {
    opacity: 0;
    transform: translateY(calc(-1 * var(--spacing-lg))) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* --- Modal Overlay and Container --- */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(20, 20, 22, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: modal-fade-in 0.2s ease-out;
}

.modal-container {
  background-color: var(--color-bg-dialog);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 480px;
  box-shadow: var(--shadow-modal);
  border: 1px solid var(--color-border);
  max-height: 90vh;
  overflow-y: auto;
  animation: modal-slide-in 0.3s ease-out;
}

/* --- Modal Header --- */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  margin: 0;
  color: var(--color-content-accent);
}

/* --- Modal Content --- */
.modal-content {
  padding: var(--spacing-md);
}

/* --- Modal Form --- */

.form-fieldset {
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.form-group label {
  font-size: var(--font-size-sm);
  color: var(--color-content-secondary);
  font-weight: var(--font-weight-medium);
}

.form-input {
  width: 100%;
  padding: 10px var(--spacing-base);
  background-color: var(--color-bg-overlay);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  color: var(--color-content-regular);
  font-size: var(--font-size-base);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-brand);
  box-shadow: 0 0 0 2px var(--color-brand-hover);
}

/* --- Modal Actions --- */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-base);
  margin-top: var(--spacing-sm);
}

/* --- File Upload --- */
.file-upload-area {
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
}

.file-input {
  display: none;
}

.file-info {
  font-size: var(--font-size-sm);
  color: var(--color-content-mute);
}


/* --- Model-specific styles --- */
.upload-area {
  border: 2px dashed var(--color-border);
  border-radius: var(--radius-base);
  padding: var(--spacing-xxl);
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
  color: var(--color-content-secondary);
  background-color: var(--color-bg-hover);
}

.upload-area.drag-over,
.upload-area:hover {
  background-color: var(--color-bg-hover);
  border-color: var(--color-brand);
}

.upload-area p {
  margin: var(--spacing-sm) 0 0;
  font-size: var(--font-size-sm);
}

.upload-area .file-name {
  color: var(--color-brand);
  font-weight: var(--font-weight-medium);
}


/* --- Material-specific styles --- */
.material-preview {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-base);
}

.preview-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--color-content-secondary);
  font-weight: var(--font-weight-medium);
}

.preview-sphere {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 120px; /* Ensure space for the preview */
}

/* --- Upload Status --- */
.upload-status {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  background-color: var(--color-bg-overlay);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-base);
}

.upload-message {
  font-size: var(--font-size-sm);
  color: var(--color-content-secondary);
  margin: 0;
  text-align: center;
}

.progress-bar-container {
  width: 100%;
  height: var(--spacing-sm);
  background-color: var(--color-bg-hover);
  border-radius: var(--radius-xs);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--color-brand);
  border-radius: var(--radius-xs);
  transition: width 0.3s ease-in-out;
}

.upload-percentage {
  font-size: var(--font-size-xs);
  color: var(--color-content-mute);
  text-align: center;
  font-weight: var(--font-weight-medium);
}