import React from "react";
import type { LucideIcon } from "lucide-react";
import { Copy } from "lucide-react";
import "./secondary-button.css";

interface SecondaryButtonProps {
  children: React.ReactNode;
  icon?: LucideIcon;
  className?: string;
  disabled?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: "button" | "submit" | "reset";
  size?: "small" | "medium" | "large";
  fullWidth?: boolean;
  showIcon?: boolean;
  variant?: "default" | "danger";
}

export const SecondaryButton: React.FC<SecondaryButtonProps> = ({
  children,
  icon: Icon = Copy,
  className = "",
  disabled = false,
  onClick,
  type = "button",
  size = "medium",
  fullWidth = false,
  showIcon = true,
  variant = "default",
}: SecondaryButtonProps) => {
  return (
    <button
      className={`secondary-button secondary-button--${size} secondary-button--${variant} ${fullWidth ? "secondary-button--full-width" : ""} ${className}`}
      disabled={disabled}
      onClick={onClick}
      type={type}
    >
      {showIcon && Icon && <Icon className="secondary-button__icon" />}
      <span className="secondary-button__text">{children}</span>
    </button>
  );
};
