import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Check } from 'lucide-react';
import './drop-down.css';

/**
 * 下拉框选项类型
 */
interface DropDownOption {
  /** 选项值 */
  value: string | number;
  /** 选项展示文本 */
  label: string;
  /** 选项禁用状态 */
  disabled?: boolean;
}

interface DropDownProps {
  /** 选项列表 */
  options: DropDownOption[];
  /** 占位文本，未选择时显示 */
  placeholder?: string;
  /** 下拉框宽度 */
  width?: string | number;
  /** 值变化回调 */
  onChange?: (value: string | number, option: DropDownOption) => void;
  /** 默认选中的值 */
  defaultValue?: string | number;
  /** 当前选中的值（受控模式）*/
  value?: string | number;
  /** 自定义类名 */
  className?: string;
  /** 禁用状态 */
  disabled?: boolean;
  /** 下拉框尺寸 */
  size?: 'default' | 'small' | 'large';
}

export const DropDown: React.FC<DropDownProps> = ({
  options = [],
  placeholder = '请选择',
  width = '100%',
  onChange,
  defaultValue,
  value: propValue,
  className = '',
  disabled = false,
  size = 'default',
}) => {
  // 是否为受控组件
  const isControlled = propValue !== undefined;
  
  // 下拉框展开状态
  const [isOpen, setIsOpen] = useState<boolean>(false);
  
  // 内部维护的选中值（非受控模式）
  const [internalValue, setInternalValue] = useState<string | number | undefined>(defaultValue);
  
  // 获取当前有效的值（受控优先）
  const currentValue = isControlled ? propValue : internalValue;
  
  // 获取当前选中的选项
  const selectedOption = options.find(option => option.value === currentValue);
  
  // 下拉框引用，用于点击外部关闭
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // 处理选项点击事件
  const handleOptionClick = (option: DropDownOption) => {
    if (option.disabled) return;
    
    if (!isControlled) {
      setInternalValue(option.value);
    }
    
    if (onChange) {
      onChange(option.value, option);
    }
    
    setIsOpen(false);
  };
  
  // 切换下拉框展开/收起状态
  const toggleDropdown = () => {
    if (disabled) return;
    setIsOpen(!isOpen);
  };
  
  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // 样式设置
  const dropdownStyle = {
    width: typeof width === 'number' ? `${width}px` : width,
  };
  
  return (
    <div 
      className={`drop-down ${isOpen ? 'drop-down--open' : ''} ${disabled ? 'drop-down--disabled' : ''} drop-down--${size} ${className}`}
      style={dropdownStyle}
      ref={dropdownRef}
      data-layer="drop-down"
    >
      <div className="drop-down__selector" onClick={toggleDropdown}>
        <div className="drop-down__selected-value">
          {selectedOption ? selectedOption.label : <span className="drop-down__placeholder">{placeholder}</span>}
        </div>
        <div className={`drop-down__icon ${isOpen ? 'drop-down__icon--rotated' : ''}`}>
          <ChevronDown size={16} />
        </div>
      </div>
      
      {isOpen && (
        <div className="drop-down__options">
          {options.map((option, index) => (
            <div 
              key={index} 
              className={`drop-down__option ${
                option.value === currentValue ? 'drop-down__option--selected' : ''
              } ${option.disabled ? 'drop-down__option--disabled' : ''}`}
              onClick={() => handleOptionClick(option)}
            >
              <span className="drop-down__option-text">{option.label}</span>
              {option.value === currentValue && (
                <span className="drop-down__option-icon">
                  <Check size={14} />
                </span>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
