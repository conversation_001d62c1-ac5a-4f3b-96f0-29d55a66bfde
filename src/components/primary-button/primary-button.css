.primary-button {
  display: inline-flex;
  align-items: center;
  height: var(--button-height);
  gap: var(--spacing-xs);
  background: var(--color-brand);
  color: var(--color-content-invert);
  border: none;
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  transition: background 0.2s ease;
}

.primary-button:hover:not(:disabled) {
  background: var(--color-brand-hover);
}

.primary-button:disabled {
  opacity: var(--opacity-muted);
  cursor: not-allowed;
}

.primary-button__icon {
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
}

/* Size variants */
.primary-button--small {
  padding: var(--spacing-s) var(--spacing-base);
  font-size: var(--font-size-base);
}
.primary-button--large {
  padding: 10px var(--spacing-lg);
  font-size: var(--font-size-base);
}

/* Full width */
.primary-button--full-width {
  width: 100%;
  justify-content: center;
}