import React from 'react';
import { useNavigate } from 'react-router-dom';
import type { ModelData } from '../../services/api';
import './model-card.css';

interface ModelCardProps {
  model: ModelData;
  className?: string;
}

export const ModelCard: React.FC<ModelCardProps> = ({ model, className = '' }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/render/${model.id}`);
  };

  return (
    <div className={`model-card ${className}`} onClick={handleClick}>
      <h3 className="model-card__title">{model.name}</h3>
      <div className="model-card__image-container">
        {model.thumbnail ? (
          <img
            src={model.thumbnail}
            alt={model.name}
            className="model-card__image"
          />
        ) : null}
      </div>
    </div>
  );
};