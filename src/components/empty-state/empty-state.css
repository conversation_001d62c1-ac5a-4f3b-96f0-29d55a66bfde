.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 200px;
  padding: var(--spacing-xl);
}

.empty-state__content {
  text-align: center;
  max-width: 400px;
}

.empty-state__icon {
  color: var(--color-content-mute);
  margin-bottom: var(--spacing-md);
}

.empty-state__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-secondary);
  margin: 0 0 var(--spacing-sm) 0;
}

.empty-state__description {
  font-size: var(--font-size-base);
  color: var(--color-content-mute);
  margin: 0 0 var(--spacing-lg) 0;
  line-height: var(--line-height-base);
}

.empty-state__action {
  margin-top: var(--spacing-lg);
}

/* 尺寸变体 */
.empty-state--small {
  min-height: 120px;
  padding: var(--spacing-lg);
}

.empty-state--small .empty-state__icon {
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
  margin-bottom: var(--spacing-sm);
}

.empty-state--small .empty-state__title {
  font-size: var(--font-size-base);
}

.empty-state--small .empty-state__description {
  font-size: var(--font-size-sm);
}

.empty-state--medium .empty-state__icon {
  width: var(--icon-size-large);
  height: var(--icon-size-large);
}

.empty-state--large {
  min-height: 300px;
  padding: var(--spacing-xxxl);
}

.empty-state--large .empty-state__icon {
  width: 64px;
  height: 64px;
}

.empty-state--large .empty-state__title {
  font-size: var(--font-size-xl);
}

.empty-state--large .empty-state__description {
  font-size: var(--font-size-lg);
}
