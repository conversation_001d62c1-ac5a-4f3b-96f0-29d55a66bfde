import React from 'react';
import type { LucideIcon } from 'lucide-react';
import { FileX } from 'lucide-react';
import './empty-state.css';

export interface EmptyStateProps {
  /** 图标组件 */
  icon?: LucideIcon;
  /** 主标题 */
  title: string;
  /** 描述文本 */
  description?: string;
  /** 操作按钮 */
  action?: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 尺寸变体 */
  size?: 'small' | 'medium' | 'large';
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon: Icon = FileX,
  title,
  description,
  action,
  className = '',
  size = 'medium'
}) => {
  const containerClasses = [
    'empty-state',
    `empty-state--${size}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      <div className="empty-state__content">
        <Icon className="empty-state__icon" />
        <h3 className="empty-state__title">{title}</h3>
        {description && (
          <p className="empty-state__description">{description}</p>
        )}
        {action && (
          <div className="empty-state__action">
            {action}
          </div>
        )}
      </div>
    </div>
  );
};
