import React, { type FC, type ReactNode, useRef, useState, useEffect } from 'react';
import './tooltip.css';

interface TooltipProps {
  children: ReactNode;
  content: ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  className?: string;
}

export const Tooltip: FC<TooltipProps> = ({ children, content, position = 'top', className = '' }) => {
  const [isVisible, setIsVisible] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isVisible && tooltipRef.current && containerRef.current) {
      const tooltipEl = tooltipRef.current;
      const containerRect = containerRef.current.getBoundingClientRect();
      
      // 重置位置以便重新计算
      tooltipEl.style.top = '';
      tooltipEl.style.bottom = '';
      tooltipEl.style.left = '';
      tooltipEl.style.right = '';

      const tooltipRect = tooltipEl.getBoundingClientRect();
      
      // 根据position属性设置tooltip的位置
      switch (position) {
        case 'top':
          tooltipEl.style.bottom = `${containerRect.height + 8}px`;
          tooltipEl.style.left = `${(containerRect.width - tooltipRect.width) / 2}px`;
          break;
        case 'bottom':
          tooltipEl.style.top = `${containerRect.height + 8}px`;
          tooltipEl.style.left = `${(containerRect.width - tooltipRect.width) / 2}px`;
          break;
        case 'left':
          tooltipEl.style.right = `${containerRect.width + 8}px`;
          tooltipEl.style.top = `${(containerRect.height - tooltipRect.height) / 2}px`;
          break;
        case 'right':
          tooltipEl.style.left = `${containerRect.width + 8}px`;
          tooltipEl.style.top = `${(containerRect.height - tooltipRect.height) / 2}px`;
          break;
      }
    }
  }, [isVisible, position]);

  return (
    <div
      className="tooltip-container"
      ref={containerRef}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div
          className={`tooltip tooltip--${position} ${className}`}
          ref={tooltipRef}
        >
          {content}
        </div>
      )}
    </div>
  );
};