# 系统组件样式使用规范

## 核心原则

### 🚫 禁止外部样式覆盖

**所有系统组件在应用到页面中时不允许再单独自定义样式**

这意味着：
- ❌ 不允许使用内联样式 (`style={{}}`) 覆盖组件样式
- ❌ 不允许在页面CSS中针对组件类名添加样式覆盖
- ❌ 不允许使用 `!important` 强制覆盖组件样式
- ❌ 不允许通过 `className` 传入自定义样式类来覆盖组件样式

### ✅ 正确的样式定制方式

1. **使用组件提供的 props 变体**
2. **使用设计系统中定义的 CSS 变量**
3. **通过组件的预设变体满足设计需求**

## 组件变体系统

### 按钮组件

```tsx
// ✅ 正确：使用变体
<PrimaryButton variant="default" size="medium">保存</PrimaryButton>
<SecondaryButton variant="danger" size="small">删除</SecondaryButton>

// ❌ 错误：样式覆盖
<PrimaryButton className="custom-red-button">保存</PrimaryButton>
<SecondaryButton style={{ color: 'red' }}>删除</SecondaryButton>
```

### 输入框组件

```tsx
// ✅ 正确：使用宽度变体
<InputBox widthVariant="wide" size="large" />
<InputBox widthVariant="narrow" size="small" />

// ❌ 错误：内联样式
<InputBox style={{ width: '300px' }} />
<InputBox className="custom-width" />
```

### 卡片组件

```tsx
// ✅ 正确：使用预设变体
<Card variant="glass" padding="large" radius="medium">
  内容
</Card>

// ❌ 错误：样式覆盖
<Card className="custom-glass-card">内容</Card>
```

## 设计系统变量

### 使用 CSS 变量

所有组件样式必须使用 `variables.css` 中定义的变量：

```css
/* ✅ 正确：使用设计系统变量 */
.custom-component {
  color: var(--color-content-accent);
  background: var(--color-bg-primary);
  padding: var(--spacing-md);
  border-radius: var(--radius-base);
}

/* ❌ 错误：硬编码值 */
.custom-component {
  color: #ffffff;
  background: #1a1a1a;
  padding: 16px;
  border-radius: 8px;
}
```

### 禁止新增变量

- ❌ 不允许在 `variables.css` 中新增变量
- ✅ 必须使用现有变量
- ✅ 如需新变量，需要通过设计系统评审

## 组件开发规范

### 组件内部样式

1. **使用 CSS 模块或独立 CSS 文件**
2. **提供充分的 props 变体**
3. **避免内联样式（除非是动态计算值）**

```tsx
// ✅ 正确：提供变体 props
interface ButtonProps {
  variant?: 'default' | 'danger' | 'success';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
}

// ❌ 错误：依赖外部样式覆盖
interface ButtonProps {
  className?: string; // 仅用于布局，不用于样式覆盖
}
```

### 动态样式处理

对于需要动态计算的样式，使用 CSS 变量：

```tsx
// ✅ 正确：使用 CSS 变量传递动态值
<div 
  className="progress-bar"
  style={{ '--progress-width': `${progress}%` } as React.CSSProperties}
/>

// CSS
.progress-bar {
  width: var(--progress-width, 0%);
}
```

## 页面级样式规范

### 布局样式

页面CSS只能包含：
- ✅ 页面布局样式
- ✅ 页面特定的容器样式
- ✅ 组件间的间距和排列

### 禁止的页面样式

```css
/* ❌ 错误：覆盖组件样式 */
.page .button {
  background: red !important;
}

.page .materials-grid {
  grid-template-columns: repeat(auto-fill, 60px);
}

/* ✅ 正确：页面布局样式 */
.page-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
```

## 迁移指南

### 现有代码迁移

1. **识别样式覆盖**
   - 搜索 `style={{}}` 
   - 搜索页面CSS中的组件类名
   - 搜索 `!important` 声明

2. **创建组件变体**
   - 分析覆盖的样式需求
   - 在组件中添加对应的 props 变体
   - 更新组件CSS添加变体样式

3. **更新使用方式**
   - 移除内联样式和CSS覆盖
   - 使用新的 props 变体
   - 测试样式效果

### 示例迁移

```tsx
// 迁移前
<SecondaryButton 
  className="delete-button"
  style={{ color: 'red', borderColor: 'red' }}
>
  删除
</SecondaryButton>

// 迁移后
<SecondaryButton variant="danger">
  删除
</SecondaryButton>
```

## 检查清单

在提交代码前，请确认：

- [ ] 没有使用内联样式覆盖组件
- [ ] 没有在页面CSS中覆盖组件样式
- [ ] 所有样式都使用设计系统变量
- [ ] 组件提供了充分的变体选项
- [ ] 新组件遵循变体设计模式

## 工具支持

### ESLint 规则（建议）

```json
{
  "rules": {
    "react/forbid-component-props": [
      "error", 
      { "forbid": ["style"] }
    ]
  }
}
```

### 代码审查要点

1. 检查是否有样式覆盖
2. 确认使用了正确的组件变体
3. 验证CSS变量的使用
4. 确保组件的完整性和一致性

---

**记住：保持设计系统的一致性和完整性是我们的首要目标。任何样式定制都应该通过组件的设计接口来实现。**
